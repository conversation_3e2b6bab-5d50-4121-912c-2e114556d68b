'use client'
import { Faculty } from "@/types/faculty.type";
import React, { useCallback, useMemo, useState } from 'react';

import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg';
import ArrowUpIcon from '@/assets/icons/arrow-down-primary.svg';
import MapIcon from '@/assets/icons/map.svg';

import {
    Accordion,
    AccordionContent,
    AccordionHeader,
    AccordionItem,
    AccordionTitleText,
    AccordionTrigger
} from "@/components/ui/Accordion/Accordion";
import { Text } from "@/components/ui/Text/Text";
import { LocaleEnum } from "@/enums/locale.enum";
import { useAppLanguage } from "@/hooks/common/useAppLanguage";
import { APP_ROUTES } from "@/routes/appRoutes";
import { BodyPart } from "@/types/body-part.type";
import { LocalizeField } from "@/types/global.type";
import { Symptom } from "@/types/symptom.type";
import { ExternalPathString, Link, LinkProps } from "expo-router";
import { stringify } from "qs-esm";
import { useTranslation } from "react-i18next";
import { FlatList, LayoutAnimation, Platform, StyleSheet, TouchableOpacity, UIManager, View } from 'react-native';

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
}

type MedicalFacultyItemProps = {
    faculty: Faculty;
    isOpenDefault?: boolean;
    isDisabled?: boolean;
    onPress?: () => void
}

export const MedicalFacultyItem = ({ faculty, isOpenDefault = false, isDisabled = false, onPress }: MedicalFacultyItemProps) => {
    const { primaryLanguage, secondaryLanguage } = useAppLanguage()
    const name = (faculty?.name as unknown as LocalizeField<string>)
    const [isExpanded, setIsExpanded] = useState(() => isOpenDefault)

    const [isShowMore, setIsShowMore] = useState(false)

    const { t } = useTranslation()

    const handleValueChange = (value: string | string[]) => {
        setIsExpanded(Array.isArray(value) ? value.length > 0 : value !== '')
    }

    const symptoms = useMemo(() => {
        return faculty?.symptoms?.filter(symptom => (symptom as Symptom)?.name) ?? []
    }, [faculty?.symptoms])

    const displayData = useMemo(() => {
        return isShowMore ? symptoms : symptoms.slice(0, 5)
    }, [isShowMore, symptoms])

    const handleToggle = useCallback(() => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setIsShowMore(prev => !prev);
    }, []);


    const renderItem = ({ item }: { item: Symptom | string }) => {
        const symptom = item as Symptom;
        const { name, id } = symptom;
        const localizedSymptomName = name as unknown as LocalizeField<string>;

        return (
            <View key={id} style={styles.listItem}>
                <Text style={styles.bulletPoint}>•</Text>
                <Text size='body7'>{localizedSymptomName[primaryLanguage as LocaleEnum]} ({localizedSymptomName[secondaryLanguage as LocaleEnum]})</Text>
            </View>
        );
    }

    const generateGoogleMapQuery = (name: string) => {
        return stringify({ query: name })
    }

    const googleMapUrl = `https://www.google.com/maps/search/?api=1&${generateGoogleMapQuery(name?.['ja'])}`


    return (
        <Accordion
            type="single"
            variant="unfilled"
            style={[
                styles.container,
                isExpanded ? styles.containerActive : styles.containerDeActive
            ]}
            onValueChange={handleValueChange}
            defaultValue={isOpenDefault ? [`faculty-${faculty.id}`] : []}
            isCollapsible={!isDisabled}
        >
            <TouchableOpacity
                activeOpacity={isDisabled ? 0.6 : 1}
                onPress={!isDisabled ? onPress : undefined}>
                <AccordionItem value={`faculty-${faculty.id}`} style={styles.accordionItem}>
                    <AccordionHeader className={`${isDisabled ? '!opacity-100' : ''}`}>
                        <AccordionTrigger
                            disabled
                            style={styles.trigger} className={`${isDisabled ? 'pointer-events-none' : ''} data-[disabled=true]:!opacity-100 data-[disabled=true]:!cursor-default`} >
                            <AccordionTitleText >
                                <Text numberOfLines={1} size='body6' style={[
                                    styles.titleText,
                                    isExpanded ? { color: '#1157C8' } : {}
                                ]}>

                                    {name[primaryLanguage as LocaleEnum]} { }
                                    ({name[secondaryLanguage as LocaleEnum]})
                                </Text>
                            </AccordionTitleText>
                            {isExpanded ? <ArrowUpIcon
                                style={{
                                    transform: [{ rotate: '-90deg' }]
                                }} className=" size-4" /> : <ArrowDownIcon className="size-4" />}
                        </AccordionTrigger>
                    </AccordionHeader>
                    <AccordionContent style={styles.content}>
                        <Text style={{ marginBottom: 8 }} size="body6" variant="default">
                            {faculty?.bodyParts?.filter(bodyPart => (bodyPart as BodyPart)?.name).map((bodyPart) => {
                                const { name } = bodyPart as BodyPart;
                                const localizedBodyPartName = name as unknown as LocalizeField<string>;

                                return `${localizedBodyPartName[primaryLanguage as LocaleEnum]} (${localizedBodyPartName[secondaryLanguage as LocaleEnum]})`;
                            })
                                .join(', ')}
                        </Text>

                        <View >
                            <FlatList
                                data={displayData}
                                renderItem={renderItem}
                                keyExtractor={item => (item as Symptom).id}
                                scrollEnabled={false}
                            />


                            {symptoms.length > 5 && (
                                <TouchableOpacity
                                    onPress={handleToggle}
                                    activeOpacity={0.7}
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        gap: 4,
                                        marginLeft: 14
                                    }}
                                >
                                    <Text size="body6" variant="primary">
                                        {isShowMore ? t('MES-493') : t('MES-22')}
                                    </Text>

                                    <ArrowUpIcon width={16} height={16} style={{ transform: [{ rotate: isShowMore ? '180deg' : '0deg' }] }} />
                                </TouchableOpacity>
                            )}
                        </View>

                        <View className="w-full  flex flex-row items-end text-right justify-end">
                            <Link className="w-fit" href={googleMapUrl as ExternalPathString} target="_blank" style={{ width: 'auto' }}>
                                <View style={styles.mapButton}>
                                    <Text size='link3' variant="success">
                                        {t("MES-131")}
                                    </Text>

                                    <MapIcon width={18} height={18} />
                                </View>
                            </Link>
                        </View>

                        {!isOpenDefault && (<View className="w-full  flex flex-row items-end text-right justify-end">
                            <Link href={`${APP_ROUTES.MEDICAL_FACULTIES.path}/${faculty.id}` as LinkProps['href']} style={styles.buttonNavigate}>
                                <View style={[styles.mapButton]} >
                                    <Text size='link3' variant="primary">
                                        {t("MES-226")}
                                    </Text>

                                    <ArrowUpIcon width={16} height={16} style={{ transform: [{ rotate: '-90deg' }] }} />
                                </View>
                            </Link>
                        </View>
                        )}


                    </AccordionContent>
                </AccordionItem>
            </TouchableOpacity>
        </Accordion >
    );
}

const styles = StyleSheet.create({
    container: {
        borderWidth: 1,
        borderRadius: 8,
        overflow: 'hidden',
    },
    containerActive: {
        borderColor: 'transparent',
        backgroundColor: '#F1F6FE'
    },
    containerDeActive: {
        borderColor: 'transparent',
        backgroundColor: '#F9F9FC',
    },
    accordionItem: {
        backgroundColor: 'transparent',
    },
    trigger: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        backgroundColor: 'transparent',
    },
    titleText: {
        flex: 1,
        textAlign: 'left',
    },
    content: {
        padding: 15,
        paddingTop: 0,
        backgroundColor: 'transparent',
    },
    bodyPartText: {
        marginBottom: 4,
    },
    listItem: {
        display: 'flex',
        flexDirection: 'row',
        marginBottom: 8
    },
    bulletPoint: {
        fontSize: 16,
        marginRight: 8,
        lineHeight: 20,
    },
    mapButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        justifyContent: 'flex-end',
        marginTop: 4,
    },
    buttonNavigate: {
        marginTop: 8
    }
});