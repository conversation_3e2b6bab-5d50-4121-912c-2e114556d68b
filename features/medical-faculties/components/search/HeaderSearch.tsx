'use client'
import CloseIcon from '@/assets/icons/close-icon.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { Text } from "@/components/ui/Text/Text"
import { useSheetActions } from "@/contexts/SheetContext/SheetContext"
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from "@/hooks/common/useAppLanguage"
import { LocalizeField } from '@/types/global.type'
import { Keyword } from "@/types/keyword.type"
import { useEffect, useMemo } from 'react'
import { useTranslation } from "react-i18next"
import { TouchableOpacity, View } from "react-native"
import { KeywordSelectionLayout } from "./BottomSheetSelectKeyword"

type HeaderSearchProps = {
    initialKeywordSelected: Record<string, Keyword>
    onApply: (keywordSelected: Record<string, Keyword>) => void
    removeKeywordSelected: (keywordId: string) => void
    clearAllKeywordSelected: () => void
}
export const HeaderSearch: React.FC<HeaderSearchProps> = ({ initialKeywordSelected, onApply, clearAllKeywordSelected, removeKeywordSelected }) => {
    const { t } = useTranslation()

    const { primaryLanguage } = useAppLanguage()

    const { openCustomSheet, closeSheet } = useSheetActions()

    const isKeywordSelected = useMemo(() => {
        return Object.keys(initialKeywordSelected).length
    }, [initialKeywordSelected])

    const handleApply = (keywordSelected: Record<string, Keyword>) => {
        onApply(keywordSelected)
        closeSheet()
    }

    useEffect(() => {
        handleOpenSelectKeyword()
    }, [])

    const handleOpenSelectKeyword = () => {
        openCustomSheet({
            children: <KeywordSelectionLayout onApply={handleApply} />,
            baseProps: {
                snapPoints: ['70%', '70%'],
                enableDynamicSizing: false,
                enableOverDrag: false,
            },
        })
    }

    return <View className='flex-col gap-3' >
        <TouchableOpacity
            onPressIn={handleOpenSelectKeyword}
            className="flex gap-2 flex-row items-center justify-between">

            <View className="flex flex-row border border-custom-divider rounded-lg p-3 gap-3 items-center">
                <SearchInputIcon width={18} height={18} />
                <View className="flex-1 overflow-hidden">
                    <Text className="line-clamp-1" size="field1" variant="subdued">
                        {t('MES-66')}
                    </Text>
                </View>
            </View>

        </TouchableOpacity>
        {!!isKeywordSelected && (<View className='flex-col gap-2'>

            <View className='flex-row justify-between gap-2'>
                <Text size="body6" variant="default">{t('MES-67')}:</Text>
                <TouchableOpacity onPress={clearAllKeywordSelected}>
                    <Text size="body6" variant='primary'>{t('MES-74')}</Text>
                </TouchableOpacity>
            </View>

            <View className='flex-wrap gap-2 flex-row'>
                {
                    Object.values(initialKeywordSelected).map((keyword) => {
                        return <View key={keyword.id} className='px-2 py-1 w-fit rounded-lg bg-primary-50 text-primary-500 flex-row gap-1 items-center'>
                            <Text size='body7' variant="primary">{(keyword.name as unknown as LocalizeField<string>)[primaryLanguage as LocaleEnum]}</Text>
                            <TouchableOpacity onPress={() => removeKeywordSelected(keyword.id)}>
                                <CloseIcon width={18} height={18} />
                            </TouchableOpacity>
                        </View>
                    }
                    )}
            </View>
        </View>)}



    </View>
}


