import React, { useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import Animated, {
    Easing,
    useAnimatedStyle,
    useSharedValue,
    withRepeat,
    withTiming,
} from 'react-native-reanimated';

import MicrophoneIcon from '@/assets/icons/microphone-red.svg';
import PauseIcon from '@/assets/icons/pause-btn.svg';
import PlayIcon from '@/assets/icons/play-btn.svg';
import { Text } from '@/components/ui/Text/Text';
import { useTranslation } from 'react-i18next';

const NUM_BARS = 20;

export const BottomSheetVoice = () => {
    const { t } = useTranslation()

    const [recordingTime, setRecordingTime] = useState(0);

    const pulseScale = useSharedValue(1);

    const pulseOpacity = useSharedValue(1);

    const waveHeightValue = useSharedValue(1);

    const [isPlay, setIsPlay] = useState<boolean>(false)

    const animatedRingStyle = useAnimatedStyle(() => {
        return {
            transform: [{ scale: pulseScale.value }],
            opacity: pulseOpacity.value,
        };
    });

    const microWaveStyle = useAnimatedStyle(() => {
        return {
            transform: [{ scaleY: waveHeightValue.value },],
        };
    });


    useEffect(() => {
        let timer: number | undefined;

        if (isPlay) {
            pulseScale.value = withRepeat(
                withTiming(1.2, { duration: 1000, easing: Easing.out(Easing.ease) }),
                -1,
                true
            );
            pulseOpacity.value = withRepeat(
                withTiming(0.4, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
                -1,
                true
            );

            waveHeightValue.value = withRepeat(
                withTiming(10, { duration: 300 }),
                -1,
                true
            );

            timer = setInterval(() => {
                setRecordingTime((prevTime) => prevTime + 1);
            }, 1000);

        } else {
            pulseScale.value = 1;
            pulseOpacity.value = 1;
            waveHeightValue.value = 1;

        }

        return () => {
            if (timer) {
                clearInterval(timer);
            }
        };
    }, [isPlay]);

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
    };


    return (
        <View style={styles.container}>
            <View style={{ alignItems: 'center', justifyContent: 'center', height: 200, width: 200 }}>
                <Animated.View style={[styles.outerPulseRing, animatedRingStyle]} />

                <MicrophoneIcon style={{ height: 100, width: 100 }} />
            </View>
            <Text size='body3'>{formatTime(recordingTime)}</Text>

            <View className='flex-row gap-[6] items-center min-h-6 bg-primary-50 w-[60%] px-3 justify-between'>
                {
                    Array.from({ length: NUM_BARS }).map((_, index) => {

                        return <Animated.View style={[microWaveStyle]} key={index} className='w-[2px] bg-custom-danger-600 h-[2px]'>

                        </Animated.View>
                    })
                }
            </View>

            <TouchableOpacity className='mt-4' onPress={() => setIsPlay(pre => !pre)}>
                {
                    isPlay ? <PauseIcon /> : <PlayIcon />
                }
            </TouchableOpacity>

            <TouchableOpacity className='mt-4 py-3 px-5 rounded-[99px] bg-primary-50'>
                <Text size='button3' variant='primary'>{t('MES-169')}</Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FFFFFF',
        gap: 12
    },
    outerPulseRing: {
        position: 'absolute',
        width: 160,
        height: 160,
        borderRadius: '100%',
        backgroundColor: 'rgba(255, 0, 0, 0.1)',
    },

});
