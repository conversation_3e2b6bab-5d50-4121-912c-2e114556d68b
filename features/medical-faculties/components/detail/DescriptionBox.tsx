import { TextInput } from "@/components/ui/TextInput/TextInput"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Keyboard, TouchableOpacity, View } from "react-native"

import MicroPhoneIcon from '@/assets/icons/microphone-2.svg'
import { useSheetActions } from "@/contexts/SheetContext/SheetContext"
import { BottomSheetVoice } from "./BottomSheetVoice"

type DescriptionBoxProps = {
    isEditable?: boolean
}
export const DescriptionBox: React.FC<DescriptionBoxProps> = ({ isEditable = true }) => {
    const { t } = useTranslation()
    const [value, setValue] = useState<string>('')

    const [isFocus, setIsFocus] = useState<boolean>(false)

    const { openCustomSheet, closeSheet } = useSheetActions()


    const handleOpenBottomSheetVoice = () => {
        Keyboard.dismiss()
        openCustomSheet({
            children: <BottomSheetVoice />,
            baseProps: {
                snapPoints: ['80%', '80%'],
                enableDynamicSizing: false,
                enableOverDrag: false,
            },
        })
    }

    return <View className="relative h-fit">
        <TextInput
            value={value}
            onChangeText={setValue}
            multiline
            wrapperClassName={`p-0 border-transparent`}
            className="w-full rounded-lg border border-transparent focus:bg-white focus:border-primary-500 bg-[#F8F8FC] px-3 py-2 text-base "
            placeholder={t('MES-1030')}
            editable={isEditable}
            style={{ maxHeight: 265, minHeight: 265, paddingBottom: 50 }}
            onFocus={() => setIsFocus(true)}
            onBlur={() => setIsFocus(false)}
        ></TextInput>

        <TouchableOpacity onPress={handleOpenBottomSheetVoice}>
            <View className={`h-11 w-11 rounded-full ${isFocus ? 'bg-[#F8F8FC]' : 'bg-white '} absolute bottom-4 right-3 items-center justify-center`}>
                <MicroPhoneIcon className="h-5 w-5" />
            </View>
        </TouchableOpacity>
    </View>
}
