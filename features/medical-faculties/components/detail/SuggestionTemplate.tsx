import { Text } from "@/components/ui/Text/Text"
import { Trans, useTranslation } from "react-i18next"
import { View } from "react-native"

export const SuggestionTemplate = () => {
    const { t } = useTranslation()
    return <View className="flex-col gap-3 mt-2">

        <Text size='body6'>{t('MES-1029')}</Text>

        <View className="p-3 rounded-lg bg-[#F8F8FC]">
            <Text size="body7" variant="subdued">
                <Trans i18nKey="MES-1020" components={{
                    when: <Text size="body7" >{t('MES-1023')}</Text>,
                    symptom: <Text size="body7" >{t('MES-1022')}</Text>,
                    how: <Text size="body7" >{t('MES-1024')}</Text>,
                    position: <Text size="body7" >{t('MES-1025')}</Text>,
                }} />
            </Text>
        </View>

        <View className="p-3 rounded-lg bg-[#F8F8FC]">
            <Text size="body7" variant="subdued">
                <Trans i18nKey="MES-1021" components={{
                    when: <Text size="body7" >{t('MES-1027')}</Text>,
                    symptom: <Text size="body7" >{t('MES-1026')}</Text>,
                    how: <Text size="body7" >{t('MES-1028')}</Text>,
                }} />
            </Text>
        </View>
    </View>
}