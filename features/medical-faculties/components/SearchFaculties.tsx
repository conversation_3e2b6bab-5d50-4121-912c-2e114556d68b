import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { Text } from "@/components/ui/Text/Text"
import { APP_ROUTES } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { Link } from 'expo-router'
import { useTranslation } from "react-i18next"
import { TouchableOpacity, View } from "react-native"

export const SearchFaculties = () => {

    const { t } = useTranslation()


    const onPress = () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    }

    return <Link asChild className='mt-4' style={{ width: '100%' }} href={APP_ROUTES.MEDICAL_FACULTIES.children?.MEDICAL_FACULTIES_SEARCH.path}>
        <TouchableOpacity onPress={onPress}
            className="rounded-lg  w-full"
            accessibilityRole="button">

            <View className="flex flex-row border border-custom-divider rounded-lg p-3 gap-3 items-center">
                <SearchInputIcon width={18} height={18} />
                <View className="flex-1 overflow-hidden">
                    <Text className="line-clamp-1" size="field1" variant="subdued">
                        {t('MES-66')}
                    </Text>
                </View>
            </View>
        </TouchableOpacity></Link>
}